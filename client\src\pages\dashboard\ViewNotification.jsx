import React, { useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, use<PERSON>avi<PERSON>, <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Bell,
  Calendar,
  Clock,
  User,
  Check,
  Trash2,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Reply,
  Forward,
  Archive,
  Edit,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/context/auth-context";
import { useNotification } from "@/context/notification-context";

const getNotificationIcon = (type) => {
  switch (type) {
    case "assignment":
      return <AlertCircle className="h-5 w-5" />;
    case "grade":
      return <CheckCircle className="h-5 w-5" />;
    case "event":
      return <Info className="h-5 w-5" />;
    case "payment":
      return <XCircle className="h-5 w-5" />;
    case "library":
      return <Info className="h-5 w-5" />;
    default:
      return <Bell className="h-5 w-5" />;
  }
};

const getPriorityColor = (priority) => {
  switch (priority) {
    case "high":
      return "destructive";
    case "medium":
      return "warning";
    case "low":
      return "secondary";
    default:
      return "secondary";
  }
};

const ViewNotification = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    currentNotification: notification,
    loading: isLoading,
    fetchNotificationById,
    markAsRead,
    removeNotification,
  } = useNotification();

  useEffect(() => {
    if (id) {
      fetchNotificationById(id);
    }
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm("Are you sure you want to delete this notification?")) {
      try {
        await removeNotification(id);
        navigate("/dashboard/notifications");
      } catch (error) {
        console.error("Failed to delete notification:", error);
      }
    }
  };

  const handleMarkAsRead = async () => {
    try {
      await markAsRead(id);
    } catch (error) {
      console.error("Failed to mark as read:", error);
    }
  };

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="space-y-6">
          <div className="animate-pulse">
            <Skeleton className="h-8 rounded w-1/4 mb-4"></Skeleton>
            <Skeleton className="h-4 rounded w-1/2 mb-8"></Skeleton>
            <Skeleton className="h-64 rounded"></Skeleton>
          </div>
        </div>
      </Container>
    );
  }

  if (!notification) {
    return (
      <Container className="py-8">
        <div className="text-center space-y-4">
          <Bell className="h-16 w-16 text-muted-foreground mx-auto" />
          <h2 className="text-2xl font-bold">Notification not found</h2>
          <p className="text-muted-foreground">
            The notification you're looking for doesn't exist or has been
            deleted.
          </p>
          <Button asChild>
            <Link to="/dashboard/notifications">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Notifications
            </Link>
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild className="gap-2">
            <Link to="/dashboard/notifications">
              <ArrowLeft className="h-4 w-4" />
              Back to Notifications
            </Link>
          </Button>

          <div className="flex items-center gap-2">
            {notification.canEdit?.(user._id, user.role) && (
              <Button variant="outline" size="sm" asChild className="gap-2">
                <Link to={`/dashboard/notifications/${notification._id}/edit`}>
                  <Edit className="h-4 w-4" />
                  Edit
                </Link>
              </Button>
            )}
            {!notification.isReadByUser?.(user._id) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAsRead}
                className="gap-2"
              >
                <Check className="h-4 w-4" />
                Mark as Read
              </Button>
            )}
            <Button variant="outline" size="sm" className="gap-2">
              <Archive className="h-4 w-4" />
              Archive
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              className="gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Notification Content */}
        <Card>
          <CardHeader>
            <div className="space-y-4">
              {/* Title and Status */}
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  {getNotificationIcon(notification.type)}
                  <div>
                    <CardTitle className="text-xl">
                      {notification.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant={getPriorityColor(notification.priority)}>
                        {notification.priority} priority
                      </Badge>
                      <Badge variant="outline">{notification.category}</Badge>
                      {!notification.isReadByUser?.(user._id) && (
                        <Badge variant="default">Unread</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Sender Info */}
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage
                    src={
                      notification.senderInfo?.avatar ||
                      notification.sender?.avatar
                    }
                  />
                  <AvatarFallback>
                    {(
                      notification.senderInfo?.name || notification.sender?.name
                    )
                      ?.split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {notification.senderInfo?.name ||
                        notification.sender?.name}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      (
                      {notification.senderInfo?.role ||
                        notification.sender?.role}
                      )
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>
                      {notification.senderInfo?.email ||
                        notification.sender?.email}
                    </span>
                    <span>•</span>
                    <span>{notification.senderInfo?.department}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>
                      {new Date(notification.createdAt).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {notification.tags && notification.tags.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Tags:</span>
                  <div className="flex gap-1">
                    {notification.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardHeader>

          <Separator />

          <CardContent className="pt-6">
            {/* Full Content */}
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {notification.fullContent || notification.message}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2 mt-8 pt-6 border-t">
              <Button variant="outline" size="sm" className="gap-2">
                <Reply className="h-4 w-4" />
                Reply
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Forward className="h-4 w-4" />
                Forward
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </Container>
  );
};

export default ViewNotification;
