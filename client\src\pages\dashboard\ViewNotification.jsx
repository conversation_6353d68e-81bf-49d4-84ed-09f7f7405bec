import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Bell,
  Calendar,
  Clock,
  User,
  Check,
  Trash2,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Reply,
  Forward,
  Archive,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

// Mock notification data (same as in Notifications.jsx)
const mockNotifications = [
  {
    id: "1",
    title: "New Assignment Posted",
    message:
      "Mathematics assignment for Chapter 5 has been posted. Due date: March 15, 2024",
    fullContent: `Dear Students,

A new assignment has been posted for Mathematics - Chapter 5: Quadratic Equations.

Assignment Details:
- Topic: Solving Quadratic Equations using different methods
- Due Date: March 15, 2024, 11:59 PM
- Total Marks: 50
- Submission Format: PDF upload through the portal

Instructions:
1. Solve all problems showing complete working
2. Use proper mathematical notation
3. Submit before the deadline to avoid penalties
4. Contact me if you have any questions

The assignment covers:
- Factoring method
- Completing the square
- Quadratic formula
- Graphical method

Good luck with your assignment!

Best regards,
Dr. Sarah Johnson
Mathematics Department`,
    type: "assignment",
    priority: "high",
    isRead: false,
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    sender: {
      name: "Dr. Sarah Johnson",
      avatar: null,
      role: "Teacher",
      email: "<EMAIL>",
      department: "Mathematics Department",
    },
    category: "Academic",
    tags: ["Assignment", "Mathematics", "Urgent"],
  },
  {
    id: "2",
    title: "Grade Updated",
    message:
      "Your grade for Physics Quiz 3 has been updated. Check your gradebook for details.",
    fullContent: `Dear Student,

Your grade for Physics Quiz 3 has been updated in the gradebook.

Quiz Details:
- Subject: Physics
- Quiz: Chapter 7 - Electromagnetic Waves
- Date Taken: March 10, 2024
- Your Score: 42/50 (84%)
- Class Average: 38/50 (76%)

Feedback:
Excellent work on the theoretical concepts! You demonstrated a strong understanding of electromagnetic wave properties and their applications. Minor calculation errors in problems 8 and 9 cost you a few points, but overall performance is commendable.

Areas for improvement:
- Double-check unit conversions
- Review wave equation applications

Keep up the good work!

Best regards,
Prof. Michael Chen
Physics Department`,
    type: "grade",
    priority: "medium",
    isRead: false,
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    sender: {
      name: "Prof. Michael Chen",
      avatar: null,
      role: "Teacher",
      email: "<EMAIL>",
      department: "Physics Department",
    },
    category: "Academic",
    tags: ["Grade", "Physics", "Quiz"],
  },
];

const getNotificationIcon = (type) => {
  switch (type) {
    case "assignment":
      return <AlertCircle className="h-5 w-5" />;
    case "grade":
      return <CheckCircle className="h-5 w-5" />;
    case "event":
      return <Info className="h-5 w-5" />;
    case "payment":
      return <XCircle className="h-5 w-5" />;
    case "library":
      return <Info className="h-5 w-5" />;
    default:
      return <Bell className="h-5 w-5" />;
  }
};

const getPriorityColor = (priority) => {
  switch (priority) {
    case "high":
      return "destructive";
    case "medium":
      return "warning";
    case "low":
      return "secondary";
    default:
      return "secondary";
  }
};

const formatDateTime = (timestamp) => {
  return new Intl.DateTimeFormat("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  }).format(timestamp);
};

const ViewNotification = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [notification, setNotification] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    const fetchNotification = () => {
      const found = mockNotifications.find((n) => n.id === id);
      setNotification(found);
      setIsLoading(false);

      // Mark as read when viewed
      if (found && !found.isRead) {
        // In a real app, this would be an API call
        setTimeout(() => {
          toast.success({
            title: "Notification marked as read",
            description: "This notification has been marked as read.",
          });
        }, 1000);
      }
    };

    fetchNotification();
  }, [id]);

  const handleDelete = () => {
    toast.success({
      title: "Notification deleted",
      description: "The notification has been deleted successfully.",
    });
    navigate("/dashboard/notifications");
  };

  const handleMarkAsRead = () => {
    setNotification((prev) => ({ ...prev, isRead: true }));
    toast.success({
      title: "Notification marked as read",
      description: "The notification has been marked as read.",
    });
  };

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </Container>
    );
  }

  if (!notification) {
    return (
      <Container className="py-8">
        <div className="text-center space-y-4">
          <Bell className="h-16 w-16 text-muted-foreground mx-auto" />
          <h2 className="text-2xl font-bold">Notification not found</h2>
          <p className="text-muted-foreground">
            The notification you're looking for doesn't exist or has been
            deleted.
          </p>
          <Button asChild>
            <Link to="/dashboard/notifications">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Notifications
            </Link>
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild className="gap-2">
            <Link to="/dashboard/notifications">
              <ArrowLeft className="h-4 w-4" />
              Back to Notifications
            </Link>
          </Button>

          <div className="flex items-center gap-2">
            {!notification.isRead && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAsRead}
                className="gap-2"
              >
                <Check className="h-4 w-4" />
                Mark as Read
              </Button>
            )}
            <Button variant="outline" size="sm" className="gap-2">
              <Archive className="h-4 w-4" />
              Archive
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              className="gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Delete
            </Button>
          </div>
        </div>

        {/* Notification Content */}
        <Card>
          <CardHeader>
            <div className="space-y-4">
              {/* Title and Status */}
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  {getNotificationIcon(notification.type)}
                  <div>
                    <CardTitle className="text-xl">
                      {notification.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant={getPriorityColor(notification.priority)}>
                        {notification.priority} priority
                      </Badge>
                      <Badge variant="outline">{notification.category}</Badge>
                      {!notification.isRead && (
                        <Badge variant="default">Unread</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Sender Info */}
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={notification.sender.avatar} />
                  <AvatarFallback>
                    {notification.sender.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {notification.sender.name}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      ({notification.sender.role})
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{notification.sender.email}</span>
                    <span>•</span>
                    <span>{notification.sender.department}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{formatDateTime(notification.timestamp)}</span>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {notification.tags && notification.tags.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Tags:</span>
                  <div className="flex gap-1">
                    {notification.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardHeader>

          <Separator />

          <CardContent className="pt-6">
            {/* Full Content */}
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {notification.fullContent || notification.message}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2 mt-8 pt-6 border-t">
              <Button variant="outline" size="sm" className="gap-2">
                <Reply className="h-4 w-4" />
                Reply
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Forward className="h-4 w-4" />
                Forward
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </Container>
  );
};

export default ViewNotification;
