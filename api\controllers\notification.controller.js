import Notification from "../models/notification.model.js";
import User from "../models/user.model.js";

// Create a new notification
export const createNotification = async (req, res) => {
  try {
    const {
      title,
      message,
      fullContent,
      type,
      priority,
      category,
      tags,
      recipients,
      specificClass,
      specificUsers,
      scheduleType,
      scheduledDate,
      scheduledTime,
      sendEmail,
      sendSMS,
    } = req.body;

    // Validation
    if (!title || !message || !type || !priority || !recipients) {
      return res.status(400).json({
        success: false,
        message: "Title, message, type, priority, and recipients are required",
      });
    }

    // Validate specific class if recipients is specific-class
    if (recipients === "specific-class" && !specificClass) {
      return res.status(400).json({
        success: false,
        message:
          "Specific class is required when recipients is 'specific-class'",
      });
    }

    // Validate scheduled date/time if schedule type is scheduled
    if (scheduleType === "scheduled" && (!scheduledDate || !scheduledTime)) {
      return res.status(400).json({
        success: false,
        message:
          "Scheduled date and time are required for scheduled notifications",
      });
    }

    const notificationData = {
      title,
      message,
      fullContent: fullContent || message,
      type,
      priority,
      category: category || "General",
      tags: tags || [],
      recipients,
      specificClass,
      specificUsers: specificUsers || [],
      scheduleType: scheduleType || "immediate",
      scheduledDate,
      scheduledTime,
      sendEmail: sendEmail || false,
      sendSMS: sendSMS || false,
      sender: req.user._id,
      schoolId: req.user.schoolId,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get("User-Agent"),
        source: "web",
      },
    };

    // Set status based on schedule type
    if (scheduleType === "scheduled") {
      notificationData.status = "scheduled";
    } else {
      notificationData.status = "sent";
      notificationData.sentAt = new Date();
    }

    const newNotification = new Notification(notificationData);

    // Calculate total recipients (simplified for now)
    let totalRecipients = 0;
    if (recipients === "all-users") {
      totalRecipients = await User.countDocuments({
        schoolId: req.user.schoolId,
      });
    } else if (recipients === "all-students") {
      totalRecipients = await User.countDocuments({
        schoolId: req.user.schoolId,
        role: "student",
      });
    } else if (recipients === "all-teachers") {
      totalRecipients = await User.countDocuments({
        schoolId: req.user.schoolId,
        role: "teacher",
      });
    } else if (recipients === "all-parents") {
      totalRecipients = await User.countDocuments({
        schoolId: req.user.schoolId,
        role: "parent",
      });
    } else if (recipients === "specific-users") {
      totalRecipients = specificUsers.length;
    }

    newNotification.totalRecipients = totalRecipients;

    await newNotification.save();

    // Populate sender information
    await newNotification.populate("sender", "name email role");

    res.status(201).json({
      success: true,
      message:
        scheduleType === "scheduled"
          ? "Notification scheduled successfully"
          : "Notification sent successfully",
      data: newNotification,
    });
  } catch (error) {
    console.error("Create Notification Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all notifications for the current user
export const getNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 20, type, priority, isRead, search } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      type,
      priority,
      isRead: isRead === "true" ? true : isRead === "false" ? false : undefined,
      search,
    };

    const notifications = await Notification.getForUser(
      req.user._id,
      req.user.schoolId,
      options
    );

    // Get total count for pagination
    const totalQuery = {
      schoolId: req.user.schoolId,
      isActive: true,
      status: "sent",
    };
    if (type) totalQuery.type = type;
    if (priority) totalQuery.priority = priority;
    if (search) {
      totalQuery.$or = [
        { title: { $regex: search, $options: "i" } },
        { message: { $regex: search, $options: "i" } },
      ];
    }

    const total = await Notification.countDocuments(totalQuery);
    const totalPages = Math.ceil(total / options.limit);

    res.json({
      success: true,
      data: notifications,
      pagination: {
        currentPage: options.page,
        totalPages,
        totalItems: total,
        itemsPerPage: options.limit,
        hasNextPage: options.page < totalPages,
        hasPrevPage: options.page > 1,
      },
    });
  } catch (error) {
    console.error("Get Notifications Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get notifications created by the current user (for admins/teachers)
export const getMyNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, type, priority } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const query = {
      sender: req.user._id,
      schoolId: req.user.schoolId,
      isActive: true,
    };

    if (status) query.status = status;
    if (type) query.type = type;
    if (priority) query.priority = priority;

    const notifications = await Notification.find(query)
      .populate("sender", "name email role")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Notification.countDocuments(query);
    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: notifications,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1,
      },
    });
  } catch (error) {
    console.error("Get My Notifications Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get a specific notification by ID
export const getNotificationById = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findOne({
      _id: id,
      schoolId: req.user.schoolId,
      isActive: true,
    }).populate("sender", "name email role");

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    // Mark as read if not already read by this user
    if (!notification.isReadByUser()(req.user._id)) {
      await notification.markAsRead(req.user._id);
    }

    res.json({
      success: true,
      data: notification,
    });
  } catch (error) {
    console.error("Get Notification By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Update a notification
export const updateNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const notification = await Notification.findOne({
      _id: id,
      schoolId: req.user.schoolId,
      isActive: true,
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    // Check if user can edit this notification
    if (!notification.canEdit(req.user._id, req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to edit this notification",
      });
    }

    // Don't allow editing sent notifications
    if (notification.status === "sent") {
      return res.status(400).json({
        success: false,
        message: "Cannot edit notifications that have already been sent",
      });
    }

    // Update the notification
    Object.assign(notification, updateData);
    await notification.save();

    await notification.populate("sender", "name email role");

    res.json({
      success: true,
      message: "Notification updated successfully",
      data: notification,
    });
  } catch (error) {
    console.error("Update Notification Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Delete a notification (soft delete)
export const deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findOne({
      _id: id,
      schoolId: req.user.schoolId,
      isActive: true,
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    // Check if user can delete this notification
    if (!notification.canEdit(req.user._id, req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to delete this notification",
      });
    }

    // Soft delete
    notification.isActive = false;
    await notification.save();

    res.json({
      success: true,
      message: "Notification deleted successfully",
    });
  } catch (error) {
    console.error("Delete Notification Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Mark notification as read
export const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findOne({
      _id: id,
      schoolId: req.user.schoolId,
      isActive: true,
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    await notification.markAsRead(req.user._id);

    res.json({
      success: true,
      message: "Notification marked as read",
    });
  } catch (error) {
    console.error("Mark As Read Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get notification statistics
export const getNotificationStats = async (req, res) => {
  try {
    const userId = req.user._id;
    const schoolId = req.user.schoolId;

    const stats = await Notification.aggregate([
      {
        $match: {
          schoolId: schoolId,
          isActive: true,
          status: "sent",
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: {
            $sum: {
              $cond: [{ $not: { $in: [userId, "$readBy.user"] } }, 1, 0],
            },
          },
          byType: {
            $push: {
              type: "$type",
              priority: "$priority",
            },
          },
        },
      },
    ]);

    const result = stats[0] || { total: 0, unread: 0, byType: [] };

    // Count by type and priority
    const typeStats = {};
    const priorityStats = {};

    result.byType.forEach((item) => {
      typeStats[item.type] = (typeStats[item.type] || 0) + 1;
      priorityStats[item.priority] = (priorityStats[item.priority] || 0) + 1;
    });

    res.json({
      success: true,
      data: {
        total: result.total,
        unread: result.unread,
        read: result.total - result.unread,
        byType: typeStats,
        byPriority: priorityStats,
      },
    });
  } catch (error) {
    console.error("Get Notification Stats Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};
