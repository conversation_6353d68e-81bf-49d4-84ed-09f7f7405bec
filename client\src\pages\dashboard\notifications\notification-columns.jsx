import React, { useState } from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { useNavigate } from "react-router-dom";
import { useNotification } from "@/context/notification-context";
import { toast } from "sonner";
import { Bell, AlertTriangle, Calendar, Clock, Users } from "lucide-react";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";

export const NotificationColumns = () => {
  return [
    {
      accessorKey: "title",
      header: ({ column }) => (
        <SortableColumn column={column} title="Notification" />
      ),
      cell: ({ row }) => {
        const type = row.original.type;
        let icon = <Bell className="h-4 w-4 text-blue-500" />;

        if (type === "Emergency") {
          icon = <AlertTriangle className="h-4 w-4 text-red-500" />;
        } else if (type === "Event") {
          icon = <Calendar className="h-4 w-4 text-green-500" />;
        } else if (type === "Academic") {
          icon = <Clock className="h-4 w-4 text-amber-500" />;
        }

        return (
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">{icon}</div>
            <div className="flex flex-col">
              <div className="font-medium">{row.getValue("title")}</div>
              <div className="text-sm text-muted-foreground line-clamp-1">
                {row.original.message}
              </div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "type",
      header: ({ column }) => <SortableColumn column={column} title="Type" />,
      cell: ({ row }) => (
        <div className="text-sm uppercase text-center w-full">
          {row.original.type}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "priority",
      header: ({ column }) => (
        <SortableColumn column={column} title="Priority" />
      ),
      cell: ({ row }) => (
        <StatusColumn
          row={row}
          statusField="priority"
          variant={{
            High: "destructive",
            Medium: "warning",
            Low: "secondary",
          }}
        />
      ),
      enableSorting: true,
    },
    {
      accessorKey: "recipients",
      header: ({ column }) => (
        <SortableColumn column={column} title="Recipients" />
      ),
      cell: ({ row }) => {
        const recipients = row.original.recipients;
        let recipientText = "Unknown";
        let icon = <Users className="h-4 w-4" />;

        switch (recipients) {
          case "all-users":
            recipientText = "All Users";
            break;
          case "all-students":
            recipientText = "All Students";
            break;
          case "all-teachers":
            recipientText = "All Teachers";
            break;
          case "all-parents":
            recipientText = "All Parents";
            break;
          case "all-admins":
            recipientText = "All Admins";
            break;
          case "specific-class":
            recipientText =
              row.original.specificClass?.name || "Specific Class";
            break;
          case "specific-users":
            recipientText = "Specific Users";
            break;
          default:
            recipientText = recipients;
        }

        return (
          <div className="flex items-center space-x-2 justify-center">
            {icon}
            <span className="text-sm">{recipientText}</span>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "scheduleType",
      header: ({ column }) => (
        <SortableColumn column={column} title="Schedule" />
      ),
      cell: ({ row }) => {
        const scheduleType = row.original.scheduleType;

        if (scheduleType === "immediate") {
          return (
            <Badge variant="outline" className="text-xs">
              Immediate
            </Badge>
          );
        } else {
          const scheduledDate = row.original.scheduledDate
            ? new Date(row.original.scheduledDate).toLocaleDateString()
            : "N/A";
          const scheduledTime = row.original.scheduledTime || "N/A";

          return (
            <div className="space-y-1 text-center">
              <Badge variant="outline" className="text-xs">
                Scheduled
              </Badge>
              <div className="text-xs text-muted-foreground">
                {scheduledDate} {scheduledTime}
              </div>
            </div>
          );
        }
      },
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        return (
          <StatusColumn
            row={row}
            statusField="status"
            variant={{
              draft: "secondary",
              scheduled: "warning",
              sent: "success",
              failed: "destructive",
            }}
          />
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "sentAt",
      header: "Sent At",
      cell: ({ row }) => {
        if (!row.original.sentAt) {
          return (
            <span className="text-sm text-muted-foreground">Not sent</span>
          );
        }
        return <DateColumn row={row} accessorKey="sentAt" />;
      },
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      id: "actions",
      cell: ({ row }) => <NotificationActions row={row} />,
    },
  ];
};

const NotificationActions = ({ row }) => {
  const navigate = useNavigate();
  const { removeNotification, sendNotificationNow } = useNotification();

  const handleView = (e) => {
    navigate(`/dashboard/notifications/${row.original._id}`);
  };

  const handleEdit = (e) => {
    // Only allow editing if not sent
    if (row.original.status === "sent") {
      toast.error("Cannot edit a notification that has already been sent");
      return;
    }
    navigate(`/dashboard/notifications/${row.original._id}/edit`);
  };

  const [openDelete, setOpenDelete] = useState(false);
  const [openSend, setOpenSend] = useState(false);

  const handleDelete = async () => {
    try {
      // Only allow deleting if not sent
      if (row.original.status === "sent") {
        toast.error("Cannot delete a notification that has already been sent");
        setOpenDelete(false);
        return;
      }

      await removeNotification(row.original._id);
      toast.success(`${row.original.title} deleted successfully.`);
      setOpenDelete(false);
    } catch (error) {
      console.error("Failed to delete notification:", error);
      toast.error("Failed to delete notification. Please try again.");
    }
  };

  const handleSend = async () => {
    try {
      // Only allow sending if not already sent
      if (row.original.status === "sent") {
        toast.error("Notification has already been sent");
        setOpenSend(false);
        return;
      }

      await sendNotificationNow(row.original._id);
      toast.success(`${row.original.title} sent successfully.`);
      setOpenSend(false);
    } catch (error) {
      console.error("Failed to send notification:", error);
      toast.error("Failed to send notification. Please try again.");
    }
  };

  return (
    <>
      <div className="flex items-center justify-end space-x-2">
        <ActionColumn
          row={row}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={() => setOpenDelete(true)}
        />

        {/* Only show send button for scheduled notifications */}
        {row.original.status === "scheduled" && (
          <button
            onClick={() => setOpenSend(true)}
            className="rounded-md p-2 hover:bg-accent"
            title="Send Now"
          >
            <Bell className="h-4 w-4" />
          </button>
        )}
      </div>

      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {row.original.title}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this notification? This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={openSend} onOpenChange={setOpenSend}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Send {row.original.title} now?</AlertDialogTitle>
            <AlertDialogDescription>
              This will send the notification immediately instead of waiting for
              the scheduled time. Are you sure you want to proceed?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSend}>Send Now</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
