import React, { createContext, useContext, useState, useEffect } from "react";
import {
  getNotifications,
  getMyNotifications,
  getNotificationById,
  createNotification,
  updateNotification,
  deleteNotification,
  markNotificationAsRead,
  getNotificationStats,
} from "@/api/notification-api";
import { toast } from "sonner";

const NotificationContext = createContext();

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error("useNotification must be used within a NotificationProvider");
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [myNotifications, setMyNotifications] = useState([]);
  const [currentNotification, setCurrentNotification] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    read: 0,
    byType: {},
    byPriority: {},
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false,
  });

  // Fetch notifications for current user
  const fetchNotifications = async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const response = await getNotifications(params);
      setNotifications(response.data);
      setPagination(response.pagination);
    } catch (error) {
      setError(error.message);
      toast.error("Failed to fetch notifications", {
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch notifications created by current user
  const fetchMyNotifications = async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const response = await getMyNotifications(params);
      setMyNotifications(response.data);
      setPagination(response.pagination);
    } catch (error) {
      setError(error.message);
      toast.error("Failed to fetch my notifications", {
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch a specific notification
  const fetchNotificationById = async (id) => {
    try {
      setLoading(true);
      setError(null);
      const response = await getNotificationById(id);
      setCurrentNotification(response.data);
      return response.data;
    } catch (error) {
      setError(error.message);
      toast.error("Failed to fetch notification", {
        description: error.message,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Create a new notification
  const addNotification = async (notificationData) => {
    try {
      setLoading(true);
      setError(null);
      const response = await createNotification(notificationData);
      
      // Add to myNotifications if it's the current user's notification
      setMyNotifications(prev => [response.data, ...prev]);
      
      toast.success("Notification created successfully!", {
        description: response.message,
      });
      
      return response.data;
    } catch (error) {
      setError(error.message);
      toast.error("Failed to create notification", {
        description: error.message,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update a notification
  const editNotification = async (id, notificationData) => {
    try {
      setLoading(true);
      setError(null);
      const response = await updateNotification(id, notificationData);
      
      // Update in myNotifications
      setMyNotifications(prev =>
        prev.map(notification =>
          notification._id === id ? response.data : notification
        )
      );
      
      // Update current notification if it's the same
      if (currentNotification?._id === id) {
        setCurrentNotification(response.data);
      }
      
      toast.success("Notification updated successfully!", {
        description: response.message,
      });
      
      return response.data;
    } catch (error) {
      setError(error.message);
      toast.error("Failed to update notification", {
        description: error.message,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Delete a notification
  const removeNotification = async (id) => {
    try {
      setLoading(true);
      setError(null);
      const response = await deleteNotification(id);
      
      // Remove from myNotifications
      setMyNotifications(prev =>
        prev.filter(notification => notification._id !== id)
      );
      
      // Remove from notifications
      setNotifications(prev =>
        prev.filter(notification => notification._id !== id)
      );
      
      toast.success("Notification deleted successfully!", {
        description: response.message,
      });
      
      return response;
    } catch (error) {
      setError(error.message);
      toast.error("Failed to delete notification", {
        description: error.message,
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (id) => {
    try {
      const response = await markNotificationAsRead(id);
      
      // Update in notifications
      setNotifications(prev =>
        prev.map(notification =>
          notification._id === id
            ? { ...notification, isRead: true }
            : notification
        )
      );
      
      // Update current notification if it's the same
      if (currentNotification?._id === id) {
        setCurrentNotification(prev => ({ ...prev, isRead: true }));
      }
      
      // Update stats
      setStats(prev => ({
        ...prev,
        unread: Math.max(0, prev.unread - 1),
        read: prev.read + 1,
      }));
      
      return response;
    } catch (error) {
      console.error("Failed to mark notification as read:", error.message);
      // Don't show toast for this as it's often called automatically
      throw error;
    }
  };

  // Fetch notification statistics
  const fetchStats = async () => {
    try {
      const response = await getNotificationStats();
      setStats(response.data);
    } catch (error) {
      console.error("Failed to fetch notification stats:", error.message);
    }
  };

  // Clear current notification
  const clearCurrentNotification = () => {
    setCurrentNotification(null);
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Fetch stats on mount
  useEffect(() => {
    fetchStats();
  }, []);

  const value = {
    // State
    notifications,
    myNotifications,
    currentNotification,
    stats,
    loading,
    error,
    pagination,

    // Actions
    fetchNotifications,
    fetchMyNotifications,
    fetchNotificationById,
    addNotification,
    editNotification,
    removeNotification,
    markAsRead,
    fetchStats,
    clearCurrentNotification,
    clearError,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
