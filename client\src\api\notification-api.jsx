import axiosInstance from "./axios-instance";

// Create a new notification
export const createNotification = async (notificationData) => {
  try {
    const response = await axiosInstance.post("/notifications/create", notificationData);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to create notification"
    );
  }
};

// Get all notifications for the current user
export const getNotifications = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();
    
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });

    const response = await axiosInstance.get(`/notifications?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch notifications"
    );
  }
};

// Get notifications created by the current user
export const getMyNotifications = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();
    
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });

    const response = await axiosInstance.get(`/notifications/my/created?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch my notifications"
    );
  }
};

// Get a specific notification by ID
export const getNotificationById = async (id) => {
  try {
    const response = await axiosInstance.get(`/notifications/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch notification"
    );
  }
};

// Update a notification
export const updateNotification = async (id, notificationData) => {
  try {
    const response = await axiosInstance.put(`/notifications/${id}`, notificationData);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to update notification"
    );
  }
};

// Delete a notification
export const deleteNotification = async (id) => {
  try {
    const response = await axiosInstance.delete(`/notifications/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to delete notification"
    );
  }
};

// Mark notification as read
export const markNotificationAsRead = async (id) => {
  try {
    const response = await axiosInstance.patch(`/notifications/${id}/read`);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to mark notification as read"
    );
  }
};

// Get notification statistics
export const getNotificationStats = async () => {
  try {
    const response = await axiosInstance.get("/notifications/stats");
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Failed to fetch notification statistics"
    );
  }
};
