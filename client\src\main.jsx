import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { ThemeProvider } from "./context/theme-context";
import { AuthProvider } from "./context/auth-context.jsx";
import { ContactProvider } from "./context/contact-context";
import { SchoolProvider } from "./context/school-context";
import { SchoolAdminProvider } from "./context/school-admin-context";
import { NotificationProvider } from "./context/notification-context";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <AuthProvider>
        <ContactProvider>
          <SchoolProvider>
            <SchoolAdminProvider>
              <NotificationProvider>
                <App />
              </NotificationProvider>
            </SchoolAdminProvider>
          </SchoolProvider>
        </ContactProvider>
      </AuthProvider>
    </ThemeProvider>
  </StrictMode>
);
