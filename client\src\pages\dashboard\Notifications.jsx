import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Bell,
  BellRing,
  Check,
  Clock,
  Eye,
  Filter,
  MoreHorizontal,
  Trash2,
  User,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Plus,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { <PERSON>rollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";

// Mock notification data
const mockNotifications = [
  {
    id: "1",
    title: "New Assignment Posted",
    message:
      "Mathematics assignment for Chapter 5 has been posted. Due date: March 15, 2024",
    type: "assignment",
    priority: "high",
    isRead: false,
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    sender: {
      name: "Dr. Sarah Johnson",
      avatar: null,
      role: "Teacher",
    },
  },
  {
    id: "2",
    title: "Grade Updated",
    message:
      "Your grade for Physics Quiz 3 has been updated. Check your gradebook for details.",
    type: "grade",
    priority: "medium",
    isRead: false,
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    sender: {
      name: "Prof. Michael Chen",
      avatar: null,
      role: "Teacher",
    },
  },
  {
    id: "3",
    title: "School Event Reminder",
    message:
      "Annual Science Fair is scheduled for tomorrow at 9:00 AM in the main auditorium.",
    type: "event",
    priority: "medium",
    isRead: true,
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    sender: {
      name: "School Administration",
      avatar: null,
      role: "Admin",
    },
  },
  {
    id: "4",
    title: "Fee Payment Reminder",
    message:
      "Your tuition fee payment is due in 3 days. Please make the payment to avoid late fees.",
    type: "payment",
    priority: "high",
    isRead: true,
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    sender: {
      name: "Finance Department",
      avatar: null,
      role: "Admin",
    },
  },
  {
    id: "5",
    title: "Library Book Return",
    message:
      "The book 'Advanced Mathematics' is due for return tomorrow. Please return it to avoid fines.",
    type: "library",
    priority: "low",
    isRead: true,
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    sender: {
      name: "Library Staff",
      avatar: null,
      role: "Staff",
    },
  },
];

const getNotificationIcon = (type) => {
  switch (type) {
    case "assignment":
      return <AlertCircle className="h-4 w-4" />;
    case "grade":
      return <CheckCircle className="h-4 w-4" />;
    case "event":
      return <Info className="h-4 w-4" />;
    case "payment":
      return <XCircle className="h-4 w-4" />;
    case "library":
      return <Info className="h-4 w-4" />;
    default:
      return <Bell className="h-4 w-4" />;
  }
};

const getPriorityColor = (priority) => {
  switch (priority) {
    case "high":
      return "destructive";
    case "medium":
      return "warning";
    case "low":
      return "secondary";
    default:
      return "secondary";
  }
};

const formatTimeAgo = (timestamp) => {
  const now = new Date();
  const diff = now - timestamp;
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 60) {
    return `${minutes}m ago`;
  } else if (hours < 24) {
    return `${hours}h ago`;
  } else {
    return `${days}d ago`;
  }
};

const Notifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState(mockNotifications);
  const [filter, setFilter] = useState("all");

  const unreadCount = notifications.filter((n) => !n.isRead).length;
  const canCreateNotifications =
    user && ["admin", "school-admin", "teacher"].includes(user.role);

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "all") return true;
    if (filter === "unread") return !notification.isRead;
    if (filter === "read") return notification.isRead;
    return notification.type === filter;
  });

  const markAsRead = (id) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
    toast.success({
      title: "Notification marked as read",
      description: "The notification has been marked as read.",
    });
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true }))
    );
    toast.success({
      title: "All notifications marked as read",
      description: "All notifications have been marked as read.",
    });
  };

  const deleteNotification = (id) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
    toast.success({
      title: "Notification deleted",
      description: "The notification has been deleted successfully.",
    });
  };

  return (
    <Container className="py-8">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <BellRing className="h-8 w-8 text-primary" />
              <h1 className="text-3xl font-bold">Notifications</h1>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {unreadCount} new
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground">
              Stay updated with your latest notifications and announcements
            </p>
          </div>

          <div className="flex items-center gap-2">
            {canCreateNotifications && (
              <Button asChild className="gap-2">
                <Link to="/dashboard/notifications/create">
                  <Plus className="h-4 w-4" />
                  Create Notification
                </Link>
              </Button>
            )}
            {unreadCount > 0 && (
              <Button
                onClick={markAllAsRead}
                variant="outline"
                className="gap-2"
              >
                <Check className="h-4 w-4" />
                Mark all as read
              </Button>
            )}
          </div>
        </div>

        <Separator />

        {/* Filter Section */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filter:</span>
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter notifications" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Notifications</SelectItem>
              <SelectItem value="unread">Unread</SelectItem>
              <SelectItem value="read">Read</SelectItem>
              <SelectItem value="assignment">Assignments</SelectItem>
              <SelectItem value="grade">Grades</SelectItem>
              <SelectItem value="event">Events</SelectItem>
              <SelectItem value="payment">Payments</SelectItem>
              <SelectItem value="library">Library</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Notifications List */}
        <div className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  No notifications found
                </h3>
                <p className="text-muted-foreground text-center">
                  {filter === "all"
                    ? "You don't have any notifications yet."
                    : `No ${filter} notifications found.`}
                </p>
              </CardContent>
            </Card>
          ) : (
            <ScrollArea className="h-[600px]">
              <div className="space-y-3">
                {filteredNotifications.map((notification) => (
                  <Card
                    key={notification.id}
                    className={`transition-all hover:shadow-md ${
                      !notification.isRead
                        ? "border-l-4 border-l-primary bg-primary/5"
                        : ""
                    }`}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        {/* Avatar */}
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={notification.sender.avatar} />
                          <AvatarFallback>
                            {notification.sender.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>

                        {/* Content */}
                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                {getNotificationIcon(notification.type)}
                                <h3 className="font-semibold text-sm">
                                  {notification.title}
                                </h3>
                                <Badge
                                  variant={getPriorityColor(
                                    notification.priority
                                  )}
                                  className="text-xs"
                                >
                                  {notification.priority}
                                </Badge>
                                {!notification.isRead && (
                                  <div className="h-2 w-2 bg-primary rounded-full" />
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {notification.message}
                              </p>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <User className="h-3 w-3" />
                                <span>{notification.sender.name}</span>
                                <span>•</span>
                                <Clock className="h-3 w-3" />
                                <span>
                                  {formatTimeAgo(notification.timestamp)}
                                </span>
                              </div>
                            </div>

                            {/* Actions */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem asChild>
                                  <Link
                                    to={`/dashboard/notifications/${notification.id}`}
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                {!notification.isRead && (
                                  <DropdownMenuItem
                                    onClick={() => markAsRead(notification.id)}
                                  >
                                    <Check className="mr-2 h-4 w-4" />
                                    Mark as Read
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() =>
                                    deleteNotification(notification.id)
                                  }
                                  className="text-destructive"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </Container>
  );
};

export default Notifications;
