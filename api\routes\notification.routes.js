import express from "express";
import {
  createNotification,
  getNotifications,
  getMyNotifications,
  getNotificationById,
  updateNotification,
  deleteNotification,
  markAsRead,
  getNotificationStats,
} from "../controllers/notification.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

// All routes require authentication
router.use(protect);

// Public routes (all authenticated users)
router.get("/", getNotifications); // Get notifications for current user
router.get("/stats", getNotificationStats); // Get notification statistics
router.get("/:id", getNotificationById); // Get specific notification
router.patch("/:id/read", markAsRead); // Mark notification as read

// Routes for users who can create notifications (admin, school-admin, teacher)
router.post(
  "/create",
  authorize(["admin", "school-admin", "teacher"]),
  createNotification
);

router.get(
  "/my/created",
  authorize(["admin", "school-admin", "teacher"]),
  getMyNotifications
);

router.put(
  "/:id",
  authorize(["admin", "school-admin", "teacher"]),
  updateNotification
);

router.delete(
  "/:id",
  authorize(["admin", "school-admin", "teacher"]),
  deleteNotification
);

export default router;
