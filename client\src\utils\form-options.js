import {
  AlertCircle,
  Bell,
  CheckCircle,
  Info,
  Library,
  XCircle,
} from "lucide-react";

export const schoolTypes = [
  { label: "Government", value: "government" },
  { label: "Private Aided", value: "private aided" },
  { label: "Private Unaided", value: "private unaided" },
  { label: "Central Government", value: "central government" },
  { label: "Deemed University", value: "deemed university" },
  { label: "International", value: "international" },
  { label: "Other", value: "other" },
];

export const boards = [
  { label: "CBSE Delhi", value: "cbse" },
  { label: "ICSE Board", value: "icse" },
  { label: "State Education Board", value: "state" },
  { label: "Cambridge International", value: "cambridge" },
  { label: "IB Organization", value: "ib" },
  { label: "American Curriculum", value: "american" },
  { label: "Other", value: "other" },
];

export const affiliations = [
  { label: "CBSE", value: "cbse" },
  { label: "CISCE (ICSE/ISC)", value: "icse" },
  { label: "State Board", value: "state" },
  { label: "IB (International Baccalaureate)", value: "ib" },
  { label: "Cambridge International", value: "cambridge" },
  { label: "Other", value: "other" },
];

export const subscriptions = [
  { label: "Basic", value: "basic" },
  { label: "Standard", value: "standard" },
  { label: "Enterprise", value: "enterprise" },
];

export const subscriptionDurations = [
  { label: "1 Month", value: "1 month" },
  { label: "3 Month", value: "3 month" },
  { label: "6 Month", value: "6 month" },
  { label: "Yearly", value: "yearly" },
];

export const paymentMethods = [
  { label: "Credit Card", value: "credit card" },
  { label: "Paypal", value: "paypal" },
];

// school admin
export const adminRoles = [
  { label: "Super Admin", value: "Super Admin" },
  { label: "Principal", value: "Principal" },
  { label: "Vice Principal", value: "Vice Principal" },
  { label: "Admin Officer", value: "Admin Officer" },
  { label: "Accountant", value: "Accountant" },
];

export const departments = [
  { label: "Administration", value: "Administration" },
  { label: "Academic Affairs", value: "Academic Affairs" },
  { label: "Finance & Accounts", value: "Finance & Accounts" },
  { label: "Human Resources", value: "Human Resources" },
  { label: "IT", value: "IT" },
];

export const qualifications = [
  { label: "High School", value: "High School" },
  { label: "Bachelor's Degree", value: "Bachelor's Degree" },
  { label: "Master's Degree", value: "Master's Degree" },
  { label: "B.Ed", value: "B.Ed" },
  { label: "MBA", value: "MBA" },
];

export const employmentTypes = [
  { label: "Full Time", value: "Full Time" },
  { label: "Part Time", value: "Part Time" },
  { label: "Contract", value: "Contract" },
  { label: "Permanent", value: "Permanent" },
];

export const genderOptions = [
  { label: "Male", value: "Male" },
  { label: "Female", value: "Female" },
  { label: "Other", value: "Other" },
];

export const bloodGroups = [
  { label: "A+", value: "A+" },
  { label: "A-", value: "A-" },
  { label: "B+", value: "B+" },
  { label: "B-", value: "B-" },
  { label: "AB+", value: "AB+" },
  { label: "AB-", value: "AB-" },
  { label: "O+", value: "O+" },
  { label: "O-", value: "O-" },
];

export const maritalStatuses = [
  { label: "Single", value: "Single" },
  { label: "Married", value: "Married" },
  { label: "Divorced", value: "Divorced" },
];

// notification
export const recipientOptions = [
  { value: "all-users", label: "All Users" },
  { value: "all-students", label: "All Students" },
  { value: "all-teachers", label: "All Teachers" },
  { value: "all-parents", label: "All Parents" },
  { value: "specific-class", label: "Specific Class" },
  { value: "specific-users", label: "Specific Users" },
];

export const notificationTypes = [
  { label: "Assignment", value: "Assignment", icon: AlertCircle },
  { value: "Exam", label: "Exam Result", icon: CheckCircle },
  { value: "Grade", label: "Grade Update", icon: CheckCircle },
  { value: "Event", label: "School Event", icon: Info },
  { value: "Payment", label: "Payment Reminder", icon: XCircle },
  { value: "Library", label: "Library Notice", icon: Library },
  { value: "General", label: "General Announcement", icon: Bell },
];

export const priorityOptions = [
  { label: "Low Priority", value: "Low" },
  { label: "Medium Priority", value: "Medium" },
  { label: "High Priority", value: "High" },
];

export const classOptions = [
  { label: "Class 1", value: "Class 1" },
  { label: "Class 2", value: "Class 2" },
  { label: "Class 3", value: "Class 3" },
  { label: "Class 4", value: "Class 4" },
  { label: "Class 5", value: "Class 5" },
  { label: "Class 6", value: "Class 6" },
  { label: "Class 7", value: "Class 7" },
  { label: "Class 8", value: "Class 8" },
];
