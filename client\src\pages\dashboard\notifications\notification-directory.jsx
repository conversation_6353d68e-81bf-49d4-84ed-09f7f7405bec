import { useEffect } from "react";
import { useNotification } from "@/context/notification-context";
import { Bell, AlertTriangle, PlusCircle, Calendar, Clock } from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { NotificationColumns } from "@/pages/dashboard/notifications/notification-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const NotificationDirectory = () => {
  const { notifications, isLoading, fetchAllNotifications } = useNotification();

  useEffect(() => {
    fetchAllNotifications();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Notification Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Notifications" },
          ]}
          actions={[
            {
              label: "New Notification",
              icon: PlusCircle,
              href: "/dashboard/notifications/create",
            },
          ]}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Notifications"
            value={notifications.length}
            description="All notifications"
            icon={Bell}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="High Priority"
            value={
              notifications.filter(
                (notification) => notification.priority === "High"
              ).length
            }
            description="Urgent notifications"
            icon={AlertTriangle}
            isLoading={isLoading}
            trend="negative"
          />

          <StatCard
            title="Scheduled"
            value={
              notifications.filter(
                (notification) => notification.status === "scheduled"
              ).length
            }
            description="Pending delivery"
            icon={Calendar}
            isLoading={isLoading}
          />

          <StatCard
            title="Recent Notifications"
            value={
              notifications.filter((notification) => {
                const createdAt = new Date(notification.createdAt);
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return createdAt >= sevenDaysAgo;
              }).length
            }
            description="Sent in last 7 days"
            icon={Clock}
            isLoading={isLoading}
          />
        </div>
        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={notifications}
              columns={NotificationColumns()}
              model="notification"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default NotificationDirectory;
