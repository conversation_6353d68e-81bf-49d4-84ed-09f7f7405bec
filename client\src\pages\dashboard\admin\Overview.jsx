import React from "react";
import { Activity, Users, School, UserCheck } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";
import { ChartAreaInteractive } from "@/components/dashboard/chart-area-interactive";

const AdminOverview = () => {
  const { user, isLoading } = useAuth();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={isLoading}
        title="Admin Dashboard"
        subtitle="Manage your schools, users, and system settings"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Schools"
          value="25"
          description="+2 from last month"
          icon={School}
          isLoading={isLoading}
          trend="positive"
        />

        <StatCard
          title="Total Users"
          value="1,245"
          description="+180 from last month"
          icon={Users}
          isLoading={isLoading}
          trend="positive"
        />

        <StatCard
          title="Active Teachers"
          value="342"
          description="+24 from last month"
          icon={UserCheck}
          isLoading={isLoading}
          trend="positive"
        />

        <StatCard
          title="System Activity"
          value="+573"
          description="+201 from last month"
          icon={Activity}
          isLoading={isLoading}
          trend="positive"
        />
      </div>

      <ChartAreaInteractive />
    </div>
  );
};

export default AdminOverview;
