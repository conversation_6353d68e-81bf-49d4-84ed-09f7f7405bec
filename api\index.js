import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import cookieParser from "cookie-parser";
import { connectDB } from "./config/db.js";
import authRoutes from "./routes/auth.routes.js";
import contactRoutes from "./routes/contact.routes.js";
import schoolRoutes from "./routes/school.routes.js";
import schoolAdminRoutes from "./routes/school-admin.routes.js";
import notificationRoutes from "./routes/notification.routes.js";

dotenv.config();

const app = express();
app.use(express.json());
app.use(cookieParser());
app.use(cors());

connectDB();

app.use("/api/auth", authRoutes);
app.use("/api/contact", contactRoutes);
app.use("/api/schools", schoolRoutes);
app.use("/api/school-admins", schoolAdminRoutes);
app.use("/api/notifications", notificationRoutes);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
