import React from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { <PERSON>, <PERSON>, Clock, User, Settings } from "lucide-react";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { RadioInput } from "@/components/form-inputs/radio-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  classOptions,
  notificationTypes,
  priorityOptions,
  recipientOptions,
} from "@/utils/form-options";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNotification } from "@/context/notification-context";

const getNotificationIcon = (type) => {
  const notificationType = notificationTypes.find((nt) => nt.value === type);
  if (notificationType) {
    const IconComponent = notificationType.icon;
    return <IconComponent className="h-4 w-4" />;
  }
  return <Bell className="h-4 w-4" />;
};

export function NotificationForm({ editingId = null, initialData = null }) {
  const navigate = useNavigate();
  const { addNotification, editNotification, isLoading } = useNotification();
  const form = useForm({
    defaultValues: {
      title: initialData?.title || "",
      message: initialData?.message || "",
      type: initialData?.type || "General",
      priority: initialData?.priority || "High",
      recipients: initialData?.recipients || "all-users",
      specificClass: initialData?.specificClass || "",
      scheduleType: initialData?.scheduleType || "immediate",
      scheduledDate: initialData?.scheduledDate || null,
      scheduledTime: initialData?.scheduledTime || "",
      sendEmail: initialData?.sendEmail || false,
      sendSMS: initialData?.sendSMS || false,
    },
  });

  const watchedType = form.watch("type");
  const watchedRecipients = form.watch("recipients");
  const watchedScheduleType = form.watch("scheduleType");

  const onSubmit = async (data) => {
    try {
      if (editingId) {
        await editNotification(editingId, data);
        toast.success("Notification updated successfully!", {
          description: "Your notification has been updated.",
        });
        navigate("/dashboard/notifications");
      } else {
        await addNotification(data);
        toast.success("Notification sent successfully!", {
          description: "Your notification has been sent.",
        });
        if (data.scheduleType === "immediate") {
          navigate("/dashboard/notifications");
        } else {
          form.reset();
        }
      }
    } catch (error) {
      console.error("Notification submission error:", error);
      toast.error(
        editingId
          ? "Failed to update notification"
          : "Failed to send notification",
        {
          description: error.message || "Please try again later.",
        }
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <FormCard title="Basic Information" icon={Bell}>
                <div className="space-y-4">
                  <TextInput
                    form={form}
                    name="title"
                    label="Notification Title"
                    placeholder="Enter notification title"
                    validation={{
                      required: "Title is required",
                      minLength: {
                        value: 5,
                        message: "Title must be at least 5 characters",
                      },
                    }}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <SelectInput
                      form={form}
                      name="type"
                      label="Notification Type"
                      placeholder="Select notification type"
                      options={notificationTypes}
                      validation={{
                        required: "Notification type is required",
                      }}
                    />

                    <SelectInput
                      form={form}
                      name="priority"
                      label="Priority Level"
                      placeholder="Select priority"
                      options={priorityOptions}
                      validation={{
                        required: "Priority is required",
                      }}
                    />
                  </div>

                  <TextareaInput
                    form={form}
                    name="message"
                    label="Message Content"
                    placeholder="Enter your notification message..."
                    validation={{
                      required: "Message is required",
                      minLength: {
                        value: 10,
                        message: "Message must be at least 10 characters",
                      },
                    }}
                  />
                </div>
              </FormCard>

              <FormCard title="Recipients" icon={Users}>
                <div className="space-y-4">
                  <SelectInput
                    form={form}
                    name="recipients"
                    label="Send To"
                    placeholder="Select recipients"
                    options={recipientOptions}
                    validation={{
                      required: "Recipients selection is required",
                    }}
                  />

                  {watchedRecipients === "specific-class" && (
                    <SelectInput
                      form={form}
                      name="specificClass"
                      label="Select Class"
                      placeholder="Choose a class"
                      options={classOptions}
                      validation={{
                        required: "Class selection is required",
                      }}
                    />
                  )}

                  {watchedRecipients === "specific-users" && (
                    <div className="p-4 border rounded-lg bg-muted/50">
                      <p className="text-sm text-muted-foreground mb-2">
                        Specific user selection will be available in the next
                        update. For now, please use other recipient options.
                      </p>
                    </div>
                  )}
                </div>
              </FormCard>

              <FormCard title="Scheduling" icon={Clock}>
                <div className="space-y-4">
                  <RadioInput
                    form={form}
                    name="scheduleType"
                    label="When to send"
                    options={[
                      { value: "immediate", label: "Send immediately" },
                      { value: "scheduled", label: "Schedule for later" },
                    ]}
                    orientation="horizontal"
                  />

                  {watchedScheduleType === "scheduled" && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <DateInput
                        form={form}
                        name="scheduledDate"
                        label="Schedule Date"
                        validation={{
                          required: "Scheduled date is required",
                        }}
                      />

                      <TextInput
                        form={form}
                        name="scheduledTime"
                        label="Schedule Time"
                        type="time"
                        validation={{
                          required: "Scheduled time is required",
                        }}
                      />
                    </div>
                  )}
                </div>
              </FormCard>

              <FormCard title="Additional Options" icon={Settings}>
                <div className="space-y-4">
                  <CheckboxInput
                    form={form}
                    name="sendEmail"
                    label="Send email notification"
                    description="Recipients will also receive an email notification"
                  />

                  <CheckboxInput
                    form={form}
                    name="sendSMS"
                    label="Send SMS notification"
                    description="Recipients will also receive an SMS notification (if phone number is available)"
                  />
                </div>
              </FormCard>
            </div>
            <div className="space-y-6">
              <FormCard title="Preview" icon={Bell}>
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      {getNotificationIcon(watchedType)}
                      <span className="font-medium text-sm">
                        {form.watch("title") || "Notification Title"}
                      </span>
                      <Badge
                        variant={
                          form.watch("priority") === "High"
                            ? "destructive"
                            : form.watch("priority") === "Medium"
                            ? "warning"
                            : "secondary"
                        }
                        className="text-xs"
                      >
                        {form.watch("priority") || "Medium"}
                      </Badge>
                    </div>

                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {form.watch("message") ||
                        "Your notification message will appear here..."}
                    </p>

                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <User className="h-3 w-3" />
                      <span>You</span>
                      <span>•</span>
                      <Clock className="h-3 w-3" />
                      <span>Now</span>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Recipients</h4>
                    <p className="text-sm text-muted-foreground">
                      {recipientOptions.find(
                        (opt) => opt.value === watchedRecipients
                      )?.label || "All Users"}
                      {watchedRecipients === "specific-class" &&
                        form.watch("specificClass") &&
                        ` - ${
                          classOptions.find(
                            (opt) => opt.value === form.watch("specificClass")
                          )?.label
                        }`}
                    </p>
                  </div>

                  {watchedScheduleType === "scheduled" && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Scheduled</h4>
                        <p className="text-sm text-muted-foreground">
                          {form.watch("scheduledDate") &&
                          form.watch("scheduledTime")
                            ? `${new Date(
                                form.watch("scheduledDate")
                              ).toLocaleDateString()} at ${form.watch(
                                "scheduledTime"
                              )}`
                            : "Date and time not set"}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </FormCard>

              <FormFooter
                href="/notifications"
                parent=""
                title="Notification"
                editingId={editingId}
              />
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
