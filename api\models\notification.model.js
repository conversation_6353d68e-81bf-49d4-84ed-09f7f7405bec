import mongoose from "mongoose";

const NotificationSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Title is required"],
      trim: true,
    },
    message: {
      type: String,
      required: [true, "Message is required"],
      trim: true,
    },
    type: {
      type: String,
      required: [true, "Notification type is required"],
      enum: [
        "Assignment",
        "Announcement",
        "General",
        "Academic",
        "Attendance",
        "Payment",
        "Exam",
        "Fee",
        "Event",
        "Holiday",
        "Emergency",
        "Other",
      ],
      default: "General",
    },
    priority: {
      type: String,
      required: [true, "Priority is required"],
      enum: ["High", "Medium", "Low"],
      default: "Medium",
    },
    recipients: {
      type: String,
      required: [true, "Recipients are required"],
      enum: [
        "all-users",
        "all-students",
        "all-teachers",
        "all-parents",
        "all-admins",
        "specific-class",
        "specific-users",
      ],
    },
    specificClass: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Class",
      default: null,
    },
    specificUsers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    scheduleType: {
      type: String,
      enum: ["immediate", "scheduled"],
      default: "immediate",
    },
    scheduledDate: {
      type: Date,
    },
    scheduledTime: {
      type: String,
    },
    sendEmail: {
      type: Boolean,
      default: false,
    },
    sendSMS: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      enum: ["draft", "scheduled", "sent", "failed"],
      default: "draft",
    },
    sentAt: {
      type: Date,
    },
    readBy: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        readAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
    },
  },
  {
    timestamps: true,
  }
);

NotificationSchema.pre("save", function (next) {
  if (this.scheduleType === "immediate" && this.status === "draft") {
    this.status = "sent";
    this.sentAt = new Date();
  }

  if (this.scheduleType === "scheduled" && this.status === "draft") {
    this.status = "scheduled";
  }

  next();
});

export default mongoose.model("Notification", NotificationSchema);
