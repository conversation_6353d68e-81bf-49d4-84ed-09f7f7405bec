import mongoose from "mongoose";

const NotificationSchema = new mongoose.Schema(
  {
    // Basic Information
    title: {
      type: String,
      required: [true, "Notification title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"],
    },
    message: {
      type: String,
      required: [true, "Notification message is required"],
      trim: true,
      maxlength: [2000, "Message cannot exceed 2000 characters"],
    },
    fullContent: {
      type: String,
      trim: true,
      maxlength: [10000, "Full content cannot exceed 10000 characters"],
    },

    // Notification Type and Priority
    type: {
      type: String,
      required: [true, "Notification type is required"],
      enum: {
        values: [
          "assignment",
          "grade",
          "event",
          "payment",
          "library",
          "general",
        ],
        message: "Invalid notification type",
      },
      default: "general",
    },
    priority: {
      type: String,
      required: [true, "Priority is required"],

    },
    category: {
      type: String,
      trim: true,
      default: "General",
    },
    tags: [
      {
        type: String,
        trim: true,
      },
    ],

    // Sender Information
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Sender is required"],
    },
    senderInfo: {
      name: String,
      role: String,
      email: String,
      department: String,
    },

    // Recipients Configuration
    recipients: {
      type: String,
      required: [true, "Recipients selection is required"],
      enum: {
        values: [
          "all-users",
          "all-students",
          "all-teachers",
          "all-parents",
          "specific-class",
          "specific-users",
        ],
        message: "Invalid recipients selection",
      },
    },
    specificClass: {
      type: String,
      trim: true,
    },
    specificUsers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],

    // School Association
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Scheduling
    scheduleType: {
      type: String,
    },
    scheduledDate: {
      type: Date,
    },
    scheduledTime: {
      type: String,
    },
    sentAt: {
      type: Date,
    },

    // Additional Options
    sendEmail: {
      type: Boolean,
      default: false,
    },
    sendSMS: {
      type: Boolean,
      default: false,
    },

    // Status and Tracking
    status: {
      type: String,
      enum: ["draft", "scheduled", "sent", "failed"],
      default: "draft",
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Read Tracking
    readBy: [
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        readAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Delivery Statistics
    totalRecipients: {
      type: Number,
      default: 0,
    },
    deliveredCount: {
      type: Number,
      default: 0,
    },
    readCount: {
      type: Number,
      default: 0,
    },

    // Metadata
    metadata: {
      ipAddress: String,
      userAgent: String,
      source: {
        type: String,
        default: "web",
      },
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance
NotificationSchema.index({ schoolId: 1, createdAt: -1 });
NotificationSchema.index({ sender: 1, createdAt: -1 });
NotificationSchema.index({ type: 1, priority: 1 });
NotificationSchema.index({ status: 1, scheduleType: 1 });
NotificationSchema.index({ "readBy.user": 1 });

// Virtual for checking if notification is read by a specific user
NotificationSchema.virtual("isReadByUser").get(function () {
  return function (userId) {
    return this.readBy.some(
      (read) => read.user.toString() === userId.toString()
    );
  };
});

// Virtual for read percentage
NotificationSchema.virtual("readPercentage").get(function () {
  if (this.totalRecipients === 0) return 0;
  return Math.round((this.readCount / this.totalRecipients) * 100);
});

// Pre-save middleware to set sender info
NotificationSchema.pre("save", async function (next) {
  if (this.isNew && this.sender) {
    try {
      const User = mongoose.model("User");
      const sender = await User.findById(this.sender).select("name email role");
      if (sender) {
        this.senderInfo = {
          name: sender.name,
          role: sender.role,
          email: sender.email,
          department: sender.department || "",
        };
      }
    } catch (error) {
      console.error("Error setting sender info:", error);
    }
  }
  next();
});

// Method to mark as read by a user
NotificationSchema.methods.markAsRead = function (userId) {
  const existingRead = this.readBy.find(
    (read) => read.user.toString() === userId.toString()
  );
  if (!existingRead) {
    this.readBy.push({ user: userId, readAt: new Date() });
    this.readCount = this.readBy.length;
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to check if user can edit this notification
NotificationSchema.methods.canEdit = function (userId, userRole) {
  // Admin can edit any notification
  if (userRole === "admin") return true;

  // Sender can edit their own notifications if not sent yet
  if (this.sender.toString() === userId.toString() && this.status !== "sent") {
    return true;
  }

  return false;
};

// Static method to get notifications for a user
NotificationSchema.statics.getForUser = function (
  userId,
  schoolId,
  options = {}
) {
  const { page = 1, limit = 20, type, priority, isRead, search } = options;

  const skip = (page - 1) * limit;
  const query = { schoolId, isActive: true, status: "sent" };

  // Add filters
  if (type) query.type = type;
  if (priority) query.priority = priority;
  if (search) {
    query.$or = [
      { title: { $regex: search, $options: "i" } },
      { message: { $regex: search, $options: "i" } },
    ];
  }

  // Handle read/unread filter
  if (isRead !== undefined) {
    if (isRead) {
      query["readBy.user"] = userId;
    } else {
      query["readBy.user"] = { $ne: userId };
    }
  }

  return this.find(query)
    .populate("sender", "name email role")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

export default mongoose.model("Notification", NotificationSchema);
