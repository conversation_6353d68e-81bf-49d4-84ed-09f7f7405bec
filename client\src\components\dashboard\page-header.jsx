import React from "react";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";

export function PageHeader({
  title,
  isLoading = false,
  breadcrumbs = [],
  actions = [],
  className = "",
}) {
  return (
    <div className={`flex justify-between items-center ${className}`}>
      <div className="flex flex-col space-y-2">
        {isLoading ? (
          <>
            <Skeleton className="h-8 w-48" />
            {breadcrumbs.length > 0 && (
              <div className="flex space-x-2">
                {Array(breadcrumbs.length)
                  .fill(0)
                  .map((_, index) => (
                    <div key={index}>
                      <Skeleton className="h-5 w-20" />
                      {index < breadcrumbs.length - 1 && (
                        <Skeleton className="h-5 w-4" />
                      )}
                    </div>
                  ))}
              </div>
            )}
          </>
        ) : (
          <>
            <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
            {breadcrumbs.length > 0 && (
              <div className="text-muted-foreground">
                <Breadcrumb>
                  <BreadcrumbList>
                    {breadcrumbs.map((breadcrumb, index) => (
                      <React.Fragment key={index}>
                        <BreadcrumbItem>
                          {breadcrumb.href ? (
                            <BreadcrumbLink href={breadcrumb.href}>
                              {breadcrumb.label}
                            </BreadcrumbLink>
                          ) : (
                            <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                          )}
                        </BreadcrumbItem>
                        {index < breadcrumbs.length - 1 && (
                          <BreadcrumbSeparator />
                        )}
                      </React.Fragment>
                    ))}
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            )}
          </>
        )}
      </div>
      {actions.length > 0 && (
        <div className="flex items-center gap-2">
          {isLoading
            ? Array(actions.length)
                .fill(0)
                .map((_, index) => (
                  <Skeleton key={index} className="h-9 w-24" />
                ))
            : actions.map((action, index) => (
                <Button
                  key={index}
                  onClick={action.onClick}
                  className={action.className}
                >
                  {action.icon && <action.icon className="h-3.5 w-3.5" />}
                  {action.href ? (
                    <a href={action.href}>{action.label}</a>
                  ) : (
                    action.label
                  )}
                </Button>
              ))}
        </div>
      )}
    </div>
  );
}
