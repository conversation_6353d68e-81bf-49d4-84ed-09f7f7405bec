import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useForm } from "react-hook-form";
import { 
  ArrowLeft, 
  Send, 
  Users, 
  Bell,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Calendar,
  Clock,
  User,
  School,
  GraduationCap,
  BookOpen,
  CreditCard,
  Library
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { RadioInput } from "@/components/form-inputs/radio-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { <PERSON><PERSON><PERSON> } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

// Mock data for recipients
const recipientOptions = [
  { value: "all-users", label: "All Users" },
  { value: "all-students", label: "All Students" },
  { value: "all-teachers", label: "All Teachers" },
  { value: "all-parents", label: "All Parents" },
  { value: "specific-class", label: "Specific Class" },
  { value: "specific-users", label: "Specific Users" },
];

const notificationTypes = [
  { value: "assignment", label: "Assignment", icon: AlertCircle },
  { value: "grade", label: "Grade Update", icon: CheckCircle },
  { value: "event", label: "School Event", icon: Info },
  { value: "payment", label: "Payment Reminder", icon: XCircle },
  { value: "library", label: "Library Notice", icon: Library },
  { value: "general", label: "General Announcement", icon: Bell },
];

const priorityOptions = [
  { value: "low", label: "Low Priority" },
  { value: "medium", label: "Medium Priority" },
  { value: "high", label: "High Priority" },
];

const classOptions = [
  { value: "class-1", label: "Class 1" },
  { value: "class-2", label: "Class 2" },
  { value: "class-3", label: "Class 3" },
  { value: "class-4", label: "Class 4" },
  { value: "class-5", label: "Class 5" },
  { value: "class-6", label: "Class 6" },
  { value: "class-7", label: "Class 7" },
  { value: "class-8", label: "Class 8" },
  { value: "class-9", label: "Class 9" },
  { value: "class-10", label: "Class 10" },
];

const getNotificationIcon = (type) => {
  const notificationType = notificationTypes.find(nt => nt.value === type);
  if (notificationType) {
    const IconComponent = notificationType.icon;
    return <IconComponent className="h-4 w-4" />;
  }
  return <Bell className="h-4 w-4" />;
};

const CreateNotification = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRecipients, setSelectedRecipients] = useState("all-users");

  const form = useForm({
    defaultValues: {
      title: "",
      message: "",
      type: "general",
      priority: "medium",
      recipients: "all-users",
      specificClass: "",
      scheduleType: "immediate",
      scheduledDate: null,
      scheduledTime: "",
      sendEmail: false,
      sendSMS: false,
    },
  });

  const watchedType = form.watch("type");
  const watchedRecipients = form.watch("recipients");
  const watchedScheduleType = form.watch("scheduleType");

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log("Notification data:", data);
      
      toast.success({
        title: "Notification sent successfully!",
        description: `Your ${data.type} notification has been sent to ${data.recipients.replace('-', ' ')}.`
      });
      
      navigate("/dashboard/notifications");
    } catch (error) {
      toast.error({
        title: "Failed to send notification",
        description: "Please try again later."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container className="py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <Send className="h-8 w-8 text-primary" />
              <h1 className="text-3xl font-bold">Create Notification</h1>
            </div>
            <p className="text-muted-foreground">
              Send notifications to students, teachers, parents, or specific groups
            </p>
          </div>
          
          <Button variant="ghost" asChild className="gap-2">
            <Link to="/dashboard/notifications">
              <ArrowLeft className="h-4 w-4" />
              Back to Notifications
            </Link>
          </Button>
        </div>

        <Separator />

        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-5 w-5" />
                      Notification Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <TextInput
                      form={form}
                      name="title"
                      label="Notification Title"
                      placeholder="Enter notification title"
                      validation={{
                        required: "Title is required",
                        minLength: {
                          value: 5,
                          message: "Title must be at least 5 characters"
                        }
                      }}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <SelectInput
                        form={form}
                        name="type"
                        label="Notification Type"
                        placeholder="Select notification type"
                        options={notificationTypes}
                        validation={{
                          required: "Notification type is required"
                        }}
                      />

                      <SelectInput
                        form={form}
                        name="priority"
                        label="Priority Level"
                        placeholder="Select priority"
                        options={priorityOptions}
                        validation={{
                          required: "Priority is required"
                        }}
                      />
                    </div>

                    <TextareaInput
                      form={form}
                      name="message"
                      label="Message Content"
                      placeholder="Enter your notification message..."
                      validation={{
                        required: "Message is required",
                        minLength: {
                          value: 10,
                          message: "Message must be at least 10 characters"
                        }
                      }}
                      inputProps={{
                        rows: 6
                      }}
                    />
                  </CardContent>
                </Card>

                {/* Recipients */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Recipients
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <SelectInput
                      form={form}
                      name="recipients"
                      label="Send To"
                      placeholder="Select recipients"
                      options={recipientOptions}
                      validation={{
                        required: "Recipients selection is required"
                      }}
                    />

                    {watchedRecipients === "specific-class" && (
                      <SelectInput
                        form={form}
                        name="specificClass"
                        label="Select Class"
                        placeholder="Choose a class"
                        options={classOptions}
                        validation={{
                          required: "Class selection is required"
                        }}
                      />
                    )}

                    {watchedRecipients === "specific-users" && (
                      <div className="p-4 border rounded-lg bg-muted/50">
                        <p className="text-sm text-muted-foreground mb-2">
                          Specific user selection will be available in the next update. 
                          For now, please use other recipient options.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Scheduling */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Scheduling
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <RadioInput
                      form={form}
                      name="scheduleType"
                      label="When to send"
                      options={[
                        { value: "immediate", label: "Send immediately" },
                        { value: "scheduled", label: "Schedule for later" }
                      ]}
                      orientation="horizontal"
                    />

                    {watchedScheduleType === "scheduled" && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <DateInput
                          form={form}
                          name="scheduledDate"
                          label="Schedule Date"
                          validation={{
                            required: "Scheduled date is required"
                          }}
                        />

                        <TextInput
                          form={form}
                          name="scheduledTime"
                          label="Schedule Time"
                          type="time"
                          validation={{
                            required: "Scheduled time is required"
                          }}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Additional Options */}
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Options</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <CheckboxInput
                      form={form}
                      name="sendEmail"
                      label="Send email notification"
                      description="Recipients will also receive an email notification"
                    />

                    <CheckboxInput
                      form={form}
                      name="sendSMS"
                      label="Send SMS notification"
                      description="Recipients will also receive an SMS notification (if phone number is available)"
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Preview Sidebar */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Preview</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        {getNotificationIcon(watchedType)}
                        <span className="font-medium text-sm">
                          {form.watch("title") || "Notification Title"}
                        </span>
                        <Badge variant={
                          form.watch("priority") === "high" ? "destructive" :
                          form.watch("priority") === "medium" ? "warning" : "secondary"
                        } className="text-xs">
                          {form.watch("priority") || "medium"}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {form.watch("message") || "Your notification message will appear here..."}
                      </p>
                      
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <User className="h-3 w-3" />
                        <span>You</span>
                        <span>•</span>
                        <Clock className="h-3 w-3" />
                        <span>Now</span>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Recipients</h4>
                      <p className="text-sm text-muted-foreground">
                        {recipientOptions.find(opt => opt.value === watchedRecipients)?.label || "All Users"}
                        {watchedRecipients === "specific-class" && form.watch("specificClass") && 
                          ` - ${classOptions.find(opt => opt.value === form.watch("specificClass"))?.label}`
                        }
                      </p>
                    </div>

                    {watchedScheduleType === "scheduled" && (
                      <>
                        <Separator />
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Scheduled</h4>
                          <p className="text-sm text-muted-foreground">
                            {form.watch("scheduledDate") && form.watch("scheduledTime") 
                              ? `${new Date(form.watch("scheduledDate")).toLocaleDateString()} at ${form.watch("scheduledTime")}`
                              : "Date and time not set"
                            }
                          </p>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button 
                    type="submit" 
                    className="w-full gap-2" 
                    disabled={isSubmitting}
                  >
                    <Send className="h-4 w-4" />
                    {isSubmitting ? "Sending..." : 
                     watchedScheduleType === "scheduled" ? "Schedule Notification" : "Send Notification"
                    }
                  </Button>
                  
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate("/dashboard/notifications")}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </Container>
  );
};

export default CreateNotification;
